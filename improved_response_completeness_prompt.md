# Improved ResponseCompleteness Prompt

## Current Issues:
1. Overly rigid percentage-based scoring
2. Limited consideration of response quality beyond coverage
3. Potential bias toward expected response length
4. Missing edge case handling

## Proposed Improvements:

### 1. Replace Rigid Percentages with Qualitative Descriptions

**Current:**
- 1: Covers less than 25% of key elements
- 2: Covers 25-50% of key elements

**Improved:**
- 1: **Severely Incomplete** - Misses most essential components, addresses only peripheral aspects
- 2: **Significantly Incomplete** - Covers some key points but omits several critical elements

### 2. Add Quality Dimensions Beyond Coverage

**New Evaluation Criteria:**
- **Content Coverage**: How thoroughly key topics are addressed
- **Accuracy**: Correctness of information provided
- **Organization**: Logical structure and clarity
- **Relevance**: Appropriateness of included information

### 3. Handle Edge Cases Better

**Add Guidelines For:**
- Responses that exceed expected scope (bonus consideration)
- Different but valid approaches to answering
- Factual errors that affect completeness assessment
- Concise responses that efficiently cover all key points

### 4. More Flexible Scoring Approach

**Instead of rigid percentages, use:**
- Essential elements (must be present for score ≥3)
- Important elements (presence affects score 3-4)
- Supplementary elements (can boost score to 5)

### 5. Improved Evaluation Process

**Enhanced Steps:**
1. **Identify Core Requirements**: Extract essential vs. supplementary elements from expected response
2. **Assess Coverage**: Check which core and supplementary elements are addressed
3. **Evaluate Quality**: Assess accuracy, clarity, and organization
4. **Consider Efficiency**: Recognize concise but complete responses
5. **Account for Additions**: Evaluate relevant information beyond expected scope
6. **Assign Score**: Balance coverage, quality, and efficiency

### 6. Better Instructions for LLM

**Add Specific Guidance:**
- "Focus on whether key concepts are adequately explained, not just mentioned"
- "Consider the user's likely information needs, not just the expected response"
- "Reward clear, well-organized responses even if they differ in structure from expected"
- "Don't penalize concise responses that efficiently cover all essential points"

## Sample Improved Prompt Structure:

```
## Overview
Evaluate how completely an assistant's response addresses the user's question or request, considering both content coverage and response quality.

## Evaluation Dimensions
1. **Essential Coverage**: Are the core requirements of the question addressed?
2. **Comprehensive Coverage**: Are important supporting details included?
3. **Quality**: Is the information accurate, clear, and well-organized?
4. **Efficiency**: Does the response cover key points without unnecessary verbosity?

## Scoring Guidelines
- **5 (Excellent)**: Addresses all essential elements with high quality, may include valuable additions
- **4 (Good)**: Covers essential elements well with minor gaps or quality issues
- **3 (Adequate)**: Addresses core requirements but lacks some important details or clarity
- **2 (Incomplete)**: Covers some key points but misses several important elements
- **1 (Poor)**: Fails to address most essential requirements of the question

## Special Considerations
- Concise responses that efficiently cover all key points should not be penalized
- Factual errors significantly impact completeness assessment
- Relevant additions beyond expected scope can enhance the score
- Different but valid approaches to answering should be recognized
```
