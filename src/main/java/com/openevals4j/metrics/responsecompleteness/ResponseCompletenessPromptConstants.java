package com.openevals4j.metrics.responsecompleteness;

public class ResponseCompletenessPromptConstants {

  private ResponseCompletenessPromptConstants() {}

  public static final String RESPONSE_COMPLETENESS_EVALUATION_PROMPT =
      """
          ## Overview
          Evaluate the completeness of an assistant's response based on how thoroughly it addresses all aspects, requirements, and sub-questions present in the user's input, using the expected response as a reference for what constitutes a complete answer.

          ## Input Format
          The evaluation will include:
          1. **User Question/Request**: The original query or request submitted by the user
          2. **Expected Response**: The ground truth or reference response that represents a complete answer
          3. **Assistant's Response**: The response generated by the assistant

          ## Evaluation Scale (1-5)
          | Score | Description | Criteria |
          |-------|-------------|----------|
          | 1 | **Severely Incomplete** | • Covers less than 25%% of the key elements present in the expected response<br>• Misses major topics, concepts, or requirements from the expected response<br>• Fails to address primary aspects covered in the expected response<br>• Provides only superficial coverage compared to the expected depth |
          | 2 | **Significantly Incomplete** | • Covers 25-50%% of the key elements from the expected response<br>• Misses several important topics or concepts present in the expected response<br>• Provides partial coverage of main points but lacks significant detail<br>• Notable gaps in content compared to the expected response |
          | 3 | **Partially Complete** | • Covers 50-75%% of the key elements from the expected response<br>• Addresses main topics but misses some important details or sub-topics<br>• Provides adequate coverage but not as comprehensive as the expected response<br>• Some important aspects are covered superficially |
          | 4 | **Mostly Complete** | • Covers 75-90%% of the key elements from the expected response<br>• Addresses most important topics with appropriate detail<br>• Minor omissions compared to the expected response<br>• Maintains similar depth and scope for most covered topics |
          | 5 | **Completely Comprehensive** | • Covers 90-100%% of the key elements from the expected response<br>• Matches or exceeds the scope and depth of the expected response<br>• Addresses all major topics, concepts, and requirements<br>• Provides comprehensive coverage comparable to the expected response |

          ## Evaluation Process
          1. **Analyze Expected Response**: Use the expected response to understand what constitutes complete coverage
          2. **Identify Key Components**: Break down the expected response into key components, topics, and requirements
          3. **Map Coverage**: Check which components from the expected response are addressed in the assistant's response
          4. **Assess Depth**: Evaluate whether each addressed component is covered with appropriate detail compared to the expected response
          5. **Check Missing Elements**: Identify important elements present in the expected response but missing from the assistant's response
          6. **Evaluate Comprehensiveness**: Determine how complete the assistant's response is relative to the expected response

          ## Scoring Guidelines
          - Use the expected response as the benchmark for what constitutes complete coverage
          - Compare the assistant's response against the expected response to identify covered and missing elements
          - Consider both content coverage and depth of treatment relative to the expected response
          - Key topics, concepts, or requirements present in the expected response should be addressed
          - The assistant's response doesn't need to be identical but should cover the same scope and depth
          - Additional relevant information beyond the expected response can be positive but shouldn't compensate for missing core elements

          ## Output Format
          Provide your response in the following JSON format:
          {
            "score": <number between 1 and 5>,
            "reasoning": "<detailed analysis of what was covered, what was missed, and why the score was assigned>"
          }

          User Question/Request: %s

          Expected Response: %s

          Assistant's Response: %s
          """;
}
