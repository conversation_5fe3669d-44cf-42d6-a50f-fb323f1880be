package com.openevals4j.metrics.rubrics;

public class RubricsPromptConstants {

  private RubricsPromptConstants() {}

  public static final String RUBRICS_EVALUATION_PROMPT =
      """
          ## Overview
          Evaluate the response based on the provided rubric criteria. Each criterion has specific scoring guidelines.
          Use the expected response as a reference point to understand what constitutes an ideal answer.

          ## Input Format
          The evaluation will include:
          1. **User Question**: The original query submitted by the user
          2. **Expected Response**: The ground truth or reference response that represents an ideal answer
          3. **Assistant's Answer**: The response generated by the assistant
          4. **Rubric Criteria**: A list of criteria with scoring guidelines

          ## Rubric Format
          Each rubric criterion includes:
          - **Name**: The name of the criterion
          - **Description**: What aspect of the response this criterion evaluates
          - **Scoring Guidelines**: Specific guidelines for scoring this criterion on a scale of 1-5

          ## Evaluation Process
          For each criterion in the rubric:
          - Carefully read the criterion description and scoring guidelines
          - Compare the assistant's response against both the criterion and the expected response
          - Use the expected response as a benchmark to understand what constitutes high-quality performance for each criterion
          - Evaluate how well the assistant's response meets the criterion relative to the expected response
          - Assign a score from 1-5 based on the scoring guidelines
          - Provide a brief justification for the score, referencing how the response compares to the expected response when relevant

          ## Output Format
          Provide your response in the following JSON format:
          ```json
          {
            "criteriaScores": [
              {
                "criterion": <criterion_name>,
                "score": <score_1_to_5>,
                "justification": <brief_justification>
              },
              ...
            ]
          }
          ```

          ## Rubric Criteria
          %s

          ## User Question
          %s

          ## Expected Response
          %s

          ## Assistant's Answer
          %s
          """;
}
