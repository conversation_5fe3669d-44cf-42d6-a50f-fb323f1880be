package com.openevals4j.metrics.contextualrelevancy;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.openevals4j.metrics.exception.EvaluationContextValidationException;
import com.openevals4j.metrics.models.EvaluationContext;
import com.openevals4j.metrics.models.EvaluationResult;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.output.Response;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@DisplayName("ContextualRelevancyMetric Mock Tests")
class ContextualRelevancyMetricMockTest {

  @Mock private ChatLanguageModel mockChatModel;
  @Mock private Response<AiMessage> mockResponse;
  @Mock private AiMessage mockAiMessage;

  private ContextualRelevancyMetric contextualRelevancyMetric;
  private ObjectMapper objectMapper;

  @BeforeEach
  void setUp() {
    objectMapper = new ObjectMapper();
    contextualRelevancyMetric =
        ContextualRelevancyMetric.builder()
            .evaluatorLLM(mockChatModel)
            .objectMapper(objectMapper)
            .build();
  }

  @Nested
  @DisplayName("Constructor and Builder Tests")
  class ConstructorTests {

    @Test
    @DisplayName("Should create metric with valid parameters")
    void shouldCreateMetricWithValidParameters() {
      assertNotNull(contextualRelevancyMetric);
      assertEquals("CONTEXTUAL_RELEVANCY", contextualRelevancyMetric.getMetricName().name());
    }

    @Test
    @DisplayName("Should throw exception when evaluatorLLM is null")
    void shouldThrowExceptionWhenEvaluatorLLMIsNull() {
      assertThrows(
          NullPointerException.class,
          () ->
              ContextualRelevancyMetric.builder()
                  .evaluatorLLM(null)
                  .objectMapper(objectMapper)
                  .build());
    }

    @Test
    @DisplayName("Should throw exception when objectMapper is null")
    void shouldThrowExceptionWhenObjectMapperIsNull() {
      assertThrows(
          NullPointerException.class,
          () ->
              ContextualRelevancyMetric.builder()
                  .evaluatorLLM(mockChatModel)
                  .objectMapper(null)
                  .build());
    }
  }

  @Nested
  @DisplayName("Validation Tests")
  class ValidationTests {

    @Test
    @DisplayName("Should throw exception when userInput is missing")
    void shouldThrowExceptionWhenUserInputIsMissing() {
      EvaluationContext context =
          EvaluationContext.builder()
              .retrievedContexts(List.of("Some context"))
              .build();

      assertThrows(
          EvaluationContextValidationException.class,
          () -> contextualRelevancyMetric.evaluate(context));
    }

    @Test
    @DisplayName("Should throw exception when retrievedContexts is missing")
    void shouldThrowExceptionWhenRetrievedContextsIsMissing() {
      EvaluationContext context =
          EvaluationContext.builder()
              .userInput("What is AI?")
              .build();

      assertThrows(
          EvaluationContextValidationException.class,
          () -> contextualRelevancyMetric.evaluate(context));
    }

    @Test
    @DisplayName("Should throw exception when retrievedContexts is empty")
    void shouldThrowExceptionWhenRetrievedContextsIsEmpty() {
      EvaluationContext context =
          EvaluationContext.builder()
              .userInput("What is AI?")
              .retrievedContexts(List.of())
              .build();

      assertThrows(
          EvaluationContextValidationException.class,
          () -> contextualRelevancyMetric.evaluate(context));
    }

    @Test
    @DisplayName("Should validate required fields correctly")
    void shouldValidateRequiredFieldsCorrectly() {
      List<String> requiredFields = contextualRelevancyMetric.getRequiredFieldsForValidation();
      assertEquals(2, requiredFields.size());
      assertTrue(requiredFields.contains("userInput"));
      assertTrue(requiredFields.contains("retrievedContexts"));
    }
  }

  @Nested
  @DisplayName("Evaluation Tests")
  class EvaluationTests {

    @Test
    @DisplayName("Should evaluate perfect relevancy successfully")
    void shouldEvaluatePerfectRelevancySuccessfully() throws Exception {
      // Arrange - Mock verdicts response (all relevant)
      String mockVerdictsJson = """
          [
            {
              "verdict": "yes",
              "reason": "This directly answers the question about AI."
            },
            {
              "verdict": "yes", 
              "reason": "This provides relevant information about AI applications."
            }
          ]
          """;

      // Mock reason response
      String mockReasonJson = """
          {
            "reason": "The score is 1.0 because all retrieved contexts are relevant to the user's question about AI."
          }
          """;

      when(mockAiMessage.text()).thenReturn(mockVerdictsJson, mockReasonJson);
      when(mockResponse.content()).thenReturn(mockAiMessage);
      when(mockChatModel.generate(any(UserMessage.class))).thenReturn(mockResponse);

      EvaluationContext context =
          EvaluationContext.builder()
              .userInput("What is artificial intelligence?")
              .retrievedContexts(List.of(
                  "AI is the simulation of human intelligence by machines.",
                  "AI applications include machine learning and natural language processing."))
              .build();

      // Act
      EvaluationResult result = contextualRelevancyMetric.evaluate(context);

      // Assert
      assertNotNull(result);
      assertEquals(1.0, result.getScore());
      assertEquals("The score is 1.0 because all retrieved contexts are relevant to the user's question about AI.", result.getReasoning());
      verify(mockChatModel, times(2)).generate(any(UserMessage.class));
    }

    @Test
    @DisplayName("Should evaluate partial relevancy successfully")
    void shouldEvaluatePartialRelevancySuccessfully() throws Exception {
      // Arrange - Mock verdicts response (2 out of 3 relevant)
      String mockVerdictsJson = """
          [
            {
              "verdict": "yes",
              "reason": "This directly answers the question about machine learning."
            },
            {
              "verdict": "no",
              "reason": "This is about weather and not relevant to machine learning."
            },
            {
              "verdict": "yes",
              "reason": "This provides relevant information about ML algorithms."
            }
          ]
          """;

      String mockReasonJson = """
          {
            "reason": "The score is 0.67 because 2 out of 3 retrieved contexts are relevant to machine learning."
          }
          """;

      when(mockAiMessage.text()).thenReturn(mockVerdictsJson, mockReasonJson);
      when(mockResponse.content()).thenReturn(mockAiMessage);
      when(mockChatModel.generate(any(UserMessage.class))).thenReturn(mockResponse);

      EvaluationContext context =
          EvaluationContext.builder()
              .userInput("What is machine learning?")
              .retrievedContexts(List.of(
                  "Machine learning is a subset of AI.",
                  "The weather is sunny today.",
                  "ML algorithms learn from data."))
              .build();

      // Act
      EvaluationResult result = contextualRelevancyMetric.evaluate(context);

      // Assert
      assertNotNull(result);
      assertEquals(0.6666666666666666, result.getScore(), 0.001);
      assertNotNull(result.getReasoning());
    }

    @Test
    @DisplayName("Should handle JSON parsing errors gracefully")
    void shouldHandleJsonParsingErrorsGracefully() throws Exception {
      // Arrange
      String invalidJson = "{ invalid json }";

      when(mockAiMessage.text()).thenReturn(invalidJson);
      when(mockResponse.content()).thenReturn(mockAiMessage);
      when(mockChatModel.generate(any(UserMessage.class))).thenReturn(mockResponse);

      EvaluationContext context =
          EvaluationContext.builder()
              .userInput("Test question")
              .retrievedContexts(List.of("Test context"))
              .build();

      // Act
      EvaluationResult result = contextualRelevancyMetric.evaluate(context);

      // Assert
      assertNotNull(result);
      assertEquals(0.0, result.getScore()); // Default score
      assertEquals("Unable to evaluate due to an error", result.getReasoning()); // Default reasoning
    }

    @Test
    @DisplayName("Should handle LLM exceptions gracefully")
    void shouldHandleLLMExceptionsGracefully() throws Exception {
      // Arrange
      when(mockChatModel.generate(any(UserMessage.class))).thenThrow(new RuntimeException("LLM error"));

      EvaluationContext context =
          EvaluationContext.builder()
              .userInput("Test question")
              .retrievedContexts(List.of("Test context"))
              .build();

      // Act
      EvaluationResult result = contextualRelevancyMetric.evaluate(context);

      // Assert
      assertNotNull(result);
      assertEquals(0.0, result.getScore()); // Default score
      assertEquals("Unable to evaluate due to an error", result.getReasoning()); // Default reasoning
    }

    @Test
    @DisplayName("Should evaluate zero relevancy successfully")
    void shouldEvaluateZeroRelevancySuccessfully() throws Exception {
      // Arrange - Mock verdicts response (none relevant)
      String mockVerdictsJson = """
          [
            {
              "verdict": "no",
              "reason": "This is about weather and not relevant to the question."
            },
            {
              "verdict": "no",
              "reason": "This is about sports and not relevant to the question."
            }
          ]
          """;

      String mockReasonJson = """
          {
            "reason": "The score is 0.0 because none of the retrieved contexts are relevant to the user's question."
          }
          """;

      when(mockAiMessage.text()).thenReturn(mockVerdictsJson, mockReasonJson);
      when(mockResponse.content()).thenReturn(mockAiMessage);
      when(mockChatModel.generate(any(UserMessage.class))).thenReturn(mockResponse);

      EvaluationContext context =
          EvaluationContext.builder()
              .userInput("What is quantum physics?")
              .retrievedContexts(List.of(
                  "The weather is cloudy today.",
                  "Basketball is a popular sport."))
              .build();

      // Act
      EvaluationResult result = contextualRelevancyMetric.evaluate(context);

      // Assert
      assertNotNull(result);
      assertEquals(0.0, result.getScore());
      assertNotNull(result.getReasoning());
    }
  }

  @Nested
  @DisplayName("Batch Evaluation Tests")
  class BatchEvaluationTests {

    @Test
    @DisplayName("Should evaluate batch of contexts successfully")
    void shouldEvaluateBatchOfContextsSuccessfully() throws Exception {
      // Arrange - Mock responses for batch evaluation
      String mockVerdicts1Json = """
          [{"verdict": "yes", "reason": "Relevant"}]
          """;
      String mockReason1Json = """
          {"reason": "Score is 1.0 because context is relevant."}
          """;

      String mockVerdicts2Json = """
          [{"verdict": "no", "reason": "Not relevant"}]
          """;
      String mockReason2Json = """
          {"reason": "Score is 0.0 because context is not relevant."}
          """;

      when(mockAiMessage.text()).thenReturn(
          mockVerdicts1Json, mockReason1Json,
          mockVerdicts2Json, mockReason2Json);
      when(mockResponse.content()).thenReturn(mockAiMessage);
      when(mockChatModel.generate(any(UserMessage.class))).thenReturn(mockResponse);

      List<EvaluationContext> contexts = Arrays.asList(
          EvaluationContext.builder()
              .userInput("Question 1")
              .retrievedContexts(List.of("Relevant context"))
              .build(),
          EvaluationContext.builder()
              .userInput("Question 2")
              .retrievedContexts(List.of("Irrelevant context"))
              .build()
      );

      // Act
      List<EvaluationResult> results = contextualRelevancyMetric.evaluateBatch(contexts);

      // Assert
      assertNotNull(results);
      assertEquals(2, results.size());
      assertEquals(1.0, results.get(0).getScore());
      assertEquals(0.0, results.get(1).getScore());
      verify(mockChatModel, times(4)).generate(any(UserMessage.class)); // 2 calls per evaluation
    }

    @Test
    @DisplayName("Should handle empty batch")
    void shouldHandleEmptyBatch() {
      // Act
      List<EvaluationResult> results = contextualRelevancyMetric.evaluateBatch(Arrays.asList());

      // Assert
      assertNotNull(results);
      assertTrue(results.isEmpty());
      verify(mockChatModel, never()).generate(any(UserMessage.class));
    }
  }
}
