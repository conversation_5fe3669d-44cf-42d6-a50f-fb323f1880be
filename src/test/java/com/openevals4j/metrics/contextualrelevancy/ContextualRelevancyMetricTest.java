package com.openevals4j.metrics.contextualrelevancy;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.openevals4j.metrics.models.EvaluationContext;
import com.openevals4j.metrics.models.EvaluationResult;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.googleai.GoogleAiGeminiChatModel;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

class ContextualRelevancyMetricTest {

  private ContextualRelevancyMetric contextualRelevancyMetric;

  @BeforeEach
  void setUp() {
    ChatLanguageModel chatModel =
        GoogleAiGeminiChatModel.builder()
            .apiKey("REPLACE_YOUR_API_KEY_HERE")
            .modelName("gemini-1.5-flash")
            .logRequestsAndResponses(true)
            .build();
    contextualRelevancyMetric =
        ContextualRelevancyMetric.builder()
            .evaluatorLLM(chatModel)
            .objectMapper(new ObjectMapper())
            .build();
  }

  @Test
  @Disabled("OpenAI and Gemini keys are not free")
  void evaluate_HighRelevancy() {
    EvaluationResult evaluationResult =
        contextualRelevancyMetric.evaluate(
            EvaluationContext.builder()
                .userInput("What is the capital of France?")
                .retrievedContexts(
                    List.of(
                        "The capital of France is Paris.",
                        "Paris is the largest city in France.",
                        "France is a country in Western Europe."))
                .build());

    Assertions.assertTrue(evaluationResult.getScore() >= 0.0 && evaluationResult.getScore() <= 1.0);
    Assertions.assertNotNull(evaluationResult.getReasoning());
    // Should score high as all contexts are relevant to the question about France's capital
    Assertions.assertTrue(evaluationResult.getScore() >= 0.8);
  }

  @Test
  @Disabled("OpenAI and Gemini keys are not free")
  void evaluate_LowRelevancy() {
    EvaluationResult evaluationResult =
        contextualRelevancyMetric.evaluate(
            EvaluationContext.builder()
                .userInput("What is the capital of France?")
                .retrievedContexts(
                    List.of(
                        "The weather today is sunny.",
                        "Dogs are popular pets.",
                        "Machine learning is a subset of AI."))
                .build());

    Assertions.assertTrue(evaluationResult.getScore() >= 0.0 && evaluationResult.getScore() <= 1.0);
    Assertions.assertNotNull(evaluationResult.getReasoning());
    // Should score low as none of the contexts are relevant to the question
    Assertions.assertTrue(evaluationResult.getScore() <= 0.2);
  }

  @Test
  @Disabled("OpenAI and Gemini keys are not free")
  void evaluate_MixedRelevancy() {
    EvaluationResult evaluationResult =
        contextualRelevancyMetric.evaluate(
            EvaluationContext.builder()
                .userInput("What are the benefits of exercise?")
                .retrievedContexts(
                    List.of(
                        "Exercise improves cardiovascular health.",
                        "Regular physical activity helps with weight management.",
                        "The weather forecast shows rain tomorrow.",
                        "Exercise can reduce stress and improve mood.",
                        "Cats are independent animals."))
                .build());

    Assertions.assertTrue(evaluationResult.getScore() >= 0.0 && evaluationResult.getScore() <= 1.0);
    Assertions.assertNotNull(evaluationResult.getReasoning());
    // Should score moderately as 3 out of 5 contexts are relevant (0.6)
    Assertions.assertTrue(evaluationResult.getScore() >= 0.4 && evaluationResult.getScore() <= 0.8);
  }

  @Test
  @Disabled("OpenAI and Gemini keys are not free")
  void evaluate_PerfectRelevancy() {
    EvaluationResult evaluationResult =
        contextualRelevancyMetric.evaluate(
            EvaluationContext.builder()
                .userInput("How does photosynthesis work?")
                .retrievedContexts(
                    List.of(
                        "Photosynthesis is the process by which plants convert sunlight into energy.",
                        "During photosynthesis, plants use carbon dioxide and water to produce glucose.",
                        "Chlorophyll in plant leaves captures light energy for photosynthesis.",
                        "The overall equation for photosynthesis is 6CO2 + 6H2O + light → C6H12O6 + 6O2."))
                .build());

    Assertions.assertTrue(evaluationResult.getScore() >= 0.0 && evaluationResult.getScore() <= 1.0);
    Assertions.assertNotNull(evaluationResult.getReasoning());
    // Should score perfect (1.0) as all contexts are highly relevant to photosynthesis
    Assertions.assertEquals(1.0, evaluationResult.getScore(), 0.1);
  }

  @Test
  @Disabled("OpenAI and Gemini keys are not free")
  void evaluate_TechnicalTopic() {
    EvaluationResult evaluationResult =
        contextualRelevancyMetric.evaluate(
            EvaluationContext.builder()
                .userInput("Explain machine learning algorithms.")
                .retrievedContexts(
                    List.of(
                        "Machine learning algorithms learn patterns from data.",
                        "Supervised learning uses labeled training data.",
                        "Neural networks are inspired by biological neurons.",
                        "The stock market closed higher today.",
                        "Unsupervised learning finds hidden patterns in data."))
                .build());

    Assertions.assertTrue(evaluationResult.getScore() >= 0.0 && evaluationResult.getScore() <= 1.0);
    Assertions.assertNotNull(evaluationResult.getReasoning());
    // Should score well as 4 out of 5 contexts are relevant to machine learning (0.8)
    Assertions.assertTrue(evaluationResult.getScore() >= 0.6);
  }

  @Test
  @Disabled("OpenAI and Gemini keys are not free")
  void evaluateBatch() {
    List<EvaluationContext> input =
        List.of(
            EvaluationContext.builder()
                .userInput("What is the capital of Japan?")
                .retrievedContexts(
                    List.of(
                        "Tokyo is the capital of Japan.",
                        "Japan is an island nation in East Asia."))
                .build(),
            EvaluationContext.builder()
                .userInput("What is the capital of Japan?")
                .retrievedContexts(
                    List.of(
                        "Pizza is a popular Italian dish.",
                        "Basketball was invented in 1891."))
                .build(),
            EvaluationContext.builder()
                .userInput("What is the capital of Japan?")
                .retrievedContexts(
                    List.of(
                        "Tokyo is the capital of Japan.",
                        "The weather is nice today.",
                        "Japan has a rich cultural heritage."))
                .build()
        );

    List<EvaluationResult> evaluationResults = contextualRelevancyMetric.evaluateBatch(input);

    Assertions.assertEquals(3, evaluationResults.size());
    
    for (EvaluationResult result : evaluationResults) {
      Assertions.assertTrue(result.getScore() >= 0.0 && result.getScore() <= 1.0);
      Assertions.assertNotNull(result.getReasoning());
    }

    // First context should score highest (both contexts relevant)
    Assertions.assertTrue(evaluationResults.get(0).getScore() >= 0.8);
    
    // Second context should score lowest (no relevant contexts)
    Assertions.assertTrue(evaluationResults.get(1).getScore() <= 0.2);
    
    // Third context should score moderately (2 out of 3 relevant)
    Assertions.assertTrue(evaluationResults.get(2).getScore() >= 0.4 && 
                         evaluationResults.get(2).getScore() <= 0.8);

    // Verify ordering: first > third > second
    Assertions.assertTrue(evaluationResults.get(0).getScore() > evaluationResults.get(2).getScore());
    Assertions.assertTrue(evaluationResults.get(2).getScore() > evaluationResults.get(1).getScore());
  }

  @Test
  @Disabled("OpenAI and Gemini keys are not free")
  void evaluate_SingleContext() {
    EvaluationResult evaluationResult =
        contextualRelevancyMetric.evaluate(
            EvaluationContext.builder()
                .userInput("What is artificial intelligence?")
                .retrievedContexts(
                    List.of("Artificial intelligence is the simulation of human intelligence by machines."))
                .build());

    Assertions.assertTrue(evaluationResult.getScore() >= 0.0 && evaluationResult.getScore() <= 1.0);
    Assertions.assertNotNull(evaluationResult.getReasoning());
    // Should score perfect (1.0) as the single context directly answers the question
    Assertions.assertEquals(1.0, evaluationResult.getScore(), 0.1);
  }

  @Test
  @Disabled("OpenAI and Gemini keys are not free")
  void evaluate_PartiallyRelevantContexts() {
    EvaluationResult evaluationResult =
        contextualRelevancyMetric.evaluate(
            EvaluationContext.builder()
                .userInput("What are the health benefits of drinking water?")
                .retrievedContexts(
                    List.of(
                        "Water helps maintain proper hydration levels in the body.",
                        "Drinking water can improve skin health and appearance.",
                        "Coffee is a popular beverage worldwide.",
                        "Water aids in digestion and nutrient absorption.",
                        "The ocean covers 71% of Earth's surface.",
                        "Staying hydrated helps regulate body temperature."))
                .build());

    Assertions.assertTrue(evaluationResult.getScore() >= 0.0 && evaluationResult.getScore() <= 1.0);
    Assertions.assertNotNull(evaluationResult.getReasoning());
    // Should score moderately as 4 out of 6 contexts are relevant (≈0.67)
    Assertions.assertTrue(evaluationResult.getScore() >= 0.5 && evaluationResult.getScore() <= 0.8);
  }
}
