package com.openevals4j.metrics.contextualrelevancy;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.openevals4j.metrics.exception.EvaluationContextValidationException;
import com.openevals4j.metrics.models.EvaluationContext;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.googleai.GoogleAiGeminiChatModel;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class ContextualRelevancyMetricValidationTest {

  private ContextualRelevancyMetric contextualRelevancyMetric;

  @BeforeEach
  void setUp() {
    ChatLanguageModel chatModel =
        GoogleAiGeminiChatModel.builder()
            .apiKey("REPLACE_YOUR_API_KEY_HERE")
            .modelName("gemini-1.5-flash")
            .logRequestsAndResponses(true)
            .build();
    contextualRelevancyMetric =
        ContextualRelevancyMetric.builder()
            .evaluatorLLM(chatModel)
            .objectMapper(new ObjectMapper())
            .build();
  }

  @Test
  void shouldThrowExceptionWhenUserInputIsNull() {
    EvaluationContext context =
        EvaluationContext.builder()
            .userInput(null)
            .retrievedContexts(List.of("Some context"))
            .build();

    Assertions.assertThrows(
        EvaluationContextValidationException.class,
        () -> contextualRelevancyMetric.evaluate(context));
  }

  @Test
  void shouldThrowExceptionWhenUserInputIsEmpty() {
    EvaluationContext context =
        EvaluationContext.builder()
            .userInput("")
            .retrievedContexts(List.of("Some context"))
            .build();

    Assertions.assertThrows(
        EvaluationContextValidationException.class,
        () -> contextualRelevancyMetric.evaluate(context));
  }

  @Test
  void shouldThrowExceptionWhenRetrievedContextsIsNull() {
    EvaluationContext context =
        EvaluationContext.builder()
            .userInput("What is AI?")
            .retrievedContexts(null)
            .build();

    Assertions.assertThrows(
        EvaluationContextValidationException.class,
        () -> contextualRelevancyMetric.evaluate(context));
  }

  @Test
  void shouldThrowExceptionWhenRetrievedContextsIsEmpty() {
    EvaluationContext context =
        EvaluationContext.builder()
            .userInput("What is AI?")
            .retrievedContexts(List.of())
            .build();

    Assertions.assertThrows(
        EvaluationContextValidationException.class,
        () -> contextualRelevancyMetric.evaluate(context));
  }

  @Test
  void shouldThrowExceptionWhenEvaluationContextIsNull() {
    Assertions.assertThrows(
        EvaluationContextValidationException.class,
        () -> contextualRelevancyMetric.evaluate(null));
  }

  @Test
  void shouldValidateRequiredFields() {
    List<String> requiredFields = contextualRelevancyMetric.getRequiredFieldsForValidation();
    
    Assertions.assertNotNull(requiredFields);
    Assertions.assertEquals(2, requiredFields.size());
    Assertions.assertTrue(requiredFields.contains("userInput"));
    Assertions.assertTrue(requiredFields.contains("retrievedContexts"));
  }

  @Test
  void shouldThrowExceptionForBatchWithInvalidContext() {
    List<EvaluationContext> contexts = List.of(
        EvaluationContext.builder()
            .userInput("Valid question")
            .retrievedContexts(List.of("Valid context"))
            .build(),
        EvaluationContext.builder()
            .userInput("") // Invalid - empty userInput
            .retrievedContexts(List.of("Some context"))
            .build()
    );

    Assertions.assertThrows(
        EvaluationContextValidationException.class,
        () -> contextualRelevancyMetric.evaluateBatch(contexts));
  }

  @Test
  void shouldHandleEmptyBatch() {
    List<EvaluationContext> emptyList = List.of();
    
    List<com.openevals4j.metrics.models.EvaluationResult> results = 
        contextualRelevancyMetric.evaluateBatch(emptyList);
    
    Assertions.assertNotNull(results);
    Assertions.assertTrue(results.isEmpty());
  }

  @Test
  void shouldThrowExceptionWhenUserInputIsWhitespaceOnly() {
    EvaluationContext context =
        EvaluationContext.builder()
            .userInput("   \t\n   ")
            .retrievedContexts(List.of("Some context"))
            .build();

    Assertions.assertThrows(
        EvaluationContextValidationException.class,
        () -> contextualRelevancyMetric.evaluate(context));
  }

  @Test
  void shouldThrowExceptionWhenRetrievedContextsContainsNullElements() {
    EvaluationContext context =
        EvaluationContext.builder()
            .userInput("What is AI?")
            .retrievedContexts(List.of("Valid context", null, "Another context"))
            .build();

    Assertions.assertThrows(
        EvaluationContextValidationException.class,
        () -> contextualRelevancyMetric.evaluate(context));
  }

  @Test
  void shouldThrowExceptionWhenRetrievedContextsContainsEmptyElements() {
    EvaluationContext context =
        EvaluationContext.builder()
            .userInput("What is AI?")
            .retrievedContexts(List.of("Valid context", "", "Another context"))
            .build();

    Assertions.assertThrows(
        EvaluationContextValidationException.class,
        () -> contextualRelevancyMetric.evaluate(context));
  }

  @Test
  void shouldThrowExceptionWhenBuilderMissingEvaluatorLLM() {
    Assertions.assertThrows(
        NullPointerException.class,
        () -> ContextualRelevancyMetric.builder()
            .evaluatorLLM(null)
            .objectMapper(new ObjectMapper())
            .build());
  }

  @Test
  void shouldThrowExceptionWhenBuilderMissingObjectMapper() {
    ChatLanguageModel chatModel =
        GoogleAiGeminiChatModel.builder()
            .apiKey("REPLACE_YOUR_API_KEY_HERE")
            .modelName("gemini-1.5-flash")
            .build();

    Assertions.assertThrows(
        NullPointerException.class,
        () -> ContextualRelevancyMetric.builder()
            .evaluatorLLM(chatModel)
            .objectMapper(null)
            .build());
  }

  @Test
  void shouldCreateMetricWithValidParameters() {
    ChatLanguageModel chatModel =
        GoogleAiGeminiChatModel.builder()
            .apiKey("REPLACE_YOUR_API_KEY_HERE")
            .modelName("gemini-1.5-flash")
            .build();

    ContextualRelevancyMetric metric = ContextualRelevancyMetric.builder()
        .evaluatorLLM(chatModel)
        .objectMapper(new ObjectMapper())
        .build();

    Assertions.assertNotNull(metric);
    Assertions.assertEquals("CONTEXTUAL_RELEVANCY", metric.getMetricName().name());
  }

  @Test
  void shouldValidateContextWithAllRequiredFields() {
    EvaluationContext validContext =
        EvaluationContext.builder()
            .userInput("What is machine learning?")
            .retrievedContexts(List.of(
                "Machine learning is a subset of AI.",
                "It involves algorithms that learn from data."))
            .build();

    // This should not throw an exception during validation
    // We can't test the actual evaluation without API keys, but validation should pass
    Assertions.assertDoesNotThrow(() -> {
      List<String> requiredFields = contextualRelevancyMetric.getRequiredFieldsForValidation();
      // Validation logic is internal, but we can verify required fields are correct
      Assertions.assertTrue(requiredFields.contains("userInput"));
      Assertions.assertTrue(requiredFields.contains("retrievedContexts"));
    });
  }

  @Test
  void shouldThrowExceptionWhenRetrievedContextsContainsWhitespaceOnlyElements() {
    EvaluationContext context =
        EvaluationContext.builder()
            .userInput("What is AI?")
            .retrievedContexts(List.of("Valid context", "   \t\n   ", "Another context"))
            .build();

    Assertions.assertThrows(
        EvaluationContextValidationException.class,
        () -> contextualRelevancyMetric.evaluate(context));
  }

  @Test
  void shouldAcceptValidContextWithMultipleRetrievedContexts() {
    EvaluationContext validContext =
        EvaluationContext.builder()
            .userInput("What are the benefits of renewable energy?")
            .retrievedContexts(List.of(
                "Renewable energy reduces greenhouse gas emissions.",
                "Solar and wind power are sustainable energy sources.",
                "Renewable energy creates jobs in green industries.",
                "It reduces dependence on fossil fuel imports."))
            .build();

    // This should not throw an exception during validation
    Assertions.assertDoesNotThrow(() -> {
      List<String> requiredFields = contextualRelevancyMetric.getRequiredFieldsForValidation();
      Assertions.assertEquals(2, requiredFields.size());
    });
  }
}
