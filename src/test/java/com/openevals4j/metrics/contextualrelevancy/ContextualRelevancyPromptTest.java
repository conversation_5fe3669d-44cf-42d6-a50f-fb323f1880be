package com.openevals4j.metrics.contextualrelevancy;

import static org.junit.jupiter.api.Assertions.*;

import java.util.List;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

@DisplayName("ContextualRelevancy Prompt Tests")
class ContextualRelevancyPromptTest {

  @Nested
  @DisplayName("Verdict Generation Prompt Tests")
  class VerdictGenerationPromptTests {

    @Test
    @DisplayName("Should contain all required sections")
    void shouldContainAllRequiredSections() {
      String prompt = ContextualRelevancyPromptConstants.VERDICT_GENERATION_PROMPT;

      assertNotNull(prompt);
      assertFalse(prompt.isEmpty());

      // Check for main sections
      assertTrue(prompt.contains("Given the user input and retrieval context"), "Should contain introduction");
      assertTrue(prompt.contains("IMPORTANT"), "Should contain important instructions");
      assertTrue(prompt.contains("Example"), "Should contain examples");
      assertTrue(prompt.contains("Evaluation Criteria"), "Should contain evaluation criteria");
      assertTrue(prompt.contains("Input:"), "Should contain input section");
      assertTrue(prompt.contains("Retrieval Context"), "Should contain retrieval context section");
    }

    @Test
    @DisplayName("Should have correct number of placeholders")
    void shouldHaveCorrectNumberOfPlaceholders() {
      String prompt = ContextualRelevancyPromptConstants.VERDICT_GENERATION_PROMPT;

      // Count %s placeholders
      long placeholderCount = prompt.chars().filter(ch -> ch == '%').count() / 2; // Each %s has 2 % chars
      assertEquals(3, placeholderCount, "Should have exactly 3 placeholders for input, document count, and contexts");
    }

    @Test
    @DisplayName("Should contain JSON format specification")
    void shouldContainJsonFormatSpecification() {
      String prompt = ContextualRelevancyPromptConstants.VERDICT_GENERATION_PROMPT;

      assertTrue(prompt.contains("JSON"), "Should mention JSON format");
      assertTrue(prompt.contains("verdict"), "Should specify verdict field");
      assertTrue(prompt.contains("reason"), "Should specify reason field");
      assertTrue(prompt.contains("yes"), "Should mention yes verdict");
      assertTrue(prompt.contains("no"), "Should mention no verdict");
    }

    @Test
    @DisplayName("Should include evaluation criteria")
    void shouldIncludeEvaluationCriteria() {
      String prompt = ContextualRelevancyPromptConstants.VERDICT_GENERATION_PROMPT;

      assertTrue(prompt.toLowerCase().contains("relevant"), "Should mention relevance");
      assertTrue(prompt.toLowerCase().contains("directly answers"), "Should mention direct answers");
      assertTrue(prompt.toLowerCase().contains("supporting information"), "Should mention supporting info");
      assertTrue(prompt.toLowerCase().contains("not relevant"), "Should mention irrelevance");
      assertTrue(prompt.toLowerCase().contains("unrelated"), "Should mention unrelated topics");
    }

    @Test
    @DisplayName("Should format correctly with sample inputs")
    void shouldFormatCorrectlyWithSampleInputs() {
      String userInput = "What is machine learning?";
      String documentCount = " (2 documents)";
      List<String> contexts = List.of(
          "Machine learning is a subset of AI.",
          "The weather is sunny today."
      );

      String formattedPrompt = String.format(
          ContextualRelevancyPromptConstants.VERDICT_GENERATION_PROMPT,
          userInput,
          documentCount,
          contexts
      );

      assertNotNull(formattedPrompt);
      assertTrue(formattedPrompt.contains(userInput), "Should contain user input");
      assertTrue(formattedPrompt.contains(documentCount), "Should contain document count");
      assertTrue(formattedPrompt.contains(contexts.toString()), "Should contain contexts");
      assertFalse(formattedPrompt.contains("%s"), "Should not contain unformatted placeholders");
    }
  }

  @Nested
  @DisplayName("Reason Generation Prompt Tests")
  class ReasonGenerationPromptTests {

    @Test
    @DisplayName("Should contain all required sections")
    void shouldContainAllRequiredSections() {
      String prompt = ContextualRelevancyPromptConstants.REASON_GENERATION_PROMPT;

      assertNotNull(prompt);
      assertFalse(prompt.isEmpty());

      // Check for main sections
      assertTrue(prompt.contains("Given the user input"), "Should contain introduction");
      assertTrue(prompt.contains("IMPORTANT"), "Should contain important instructions");
      assertTrue(prompt.contains("Example JSON"), "Should contain JSON example");
      assertTrue(prompt.contains("Contextual Relevancy Score"), "Should contain score section");
      assertTrue(prompt.contains("Input:"), "Should contain input section");
      assertTrue(prompt.contains("Retrieval Contexts:"), "Should contain contexts section");
    }

    @Test
    @DisplayName("Should have correct number of placeholders")
    void shouldHaveCorrectNumberOfPlaceholders() {
      String prompt = ContextualRelevancyPromptConstants.REASON_GENERATION_PROMPT;

      // Count %s placeholders
      long placeholderCount = prompt.chars().filter(ch -> ch == '%').count() / 2; // Each %s has 2 % chars
      assertEquals(3, placeholderCount, "Should have exactly 3 placeholders for score, input, and contexts");
    }

    @Test
    @DisplayName("Should contain JSON output format specification")
    void shouldContainJsonOutputFormatSpecification() {
      String prompt = ContextualRelevancyPromptConstants.REASON_GENERATION_PROMPT;

      assertTrue(prompt.contains("JSON format"), "Should mention JSON format");
      assertTrue(prompt.contains("reason"), "Should specify reason field");
      assertTrue(prompt.contains("\"reason\""), "Should show reason field in quotes");
    }

    @Test
    @DisplayName("Should include specific instructions")
    void shouldIncludeSpecificInstructions() {
      String prompt = ContextualRelevancyPromptConstants.REASON_GENERATION_PROMPT;

      assertTrue(prompt.contains("DO NOT mention 'verdict'"), "Should instruct not to mention verdict");
      assertTrue(prompt.contains("relevant/irrelevant nodes"), "Should mention nodes terminology");
      assertTrue(prompt.contains("MUST USE the information"), "Should emphasize using reason field info");
      assertTrue(prompt.contains("retrieval contexts"), "Should mention retrieval contexts");
    }

    @Test
    @DisplayName("Should handle edge cases in instructions")
    void shouldHandleEdgeCasesInInstructions() {
      String prompt = ContextualRelevancyPromptConstants.REASON_GENERATION_PROMPT;

      assertTrue(prompt.contains("If the score is 1"), "Should handle perfect score case");
      assertTrue(prompt.contains("If the score is 0"), "Should handle zero score case");
      assertTrue(prompt.contains("keep it positive"), "Should maintain positive tone");
    }

    @Test
    @DisplayName("Should format correctly with sample inputs")
    void shouldFormatCorrectlyWithSampleInputs() {
      double score = 0.75;
      String userInput = "What is AI?";
      String contexts = "[{\"verdict\":\"yes\",\"reason\":\"Relevant info\"}]";

      String formattedPrompt = String.format(
          ContextualRelevancyPromptConstants.REASON_GENERATION_PROMPT,
          score,
          userInput,
          contexts
      );

      assertNotNull(formattedPrompt);
      assertTrue(formattedPrompt.contains("0.75"), "Should contain score");
      assertTrue(formattedPrompt.contains(userInput), "Should contain user input");
      assertTrue(formattedPrompt.contains(contexts), "Should contain contexts");
      assertFalse(formattedPrompt.contains("%s"), "Should not contain unformatted placeholders");
    }
  }

  @Nested
  @DisplayName("Prompt Quality Tests")
  class PromptQualityTests {

    @Test
    @DisplayName("Should have clear instructions in verdict prompt")
    void shouldHaveClearInstructionsInVerdictPrompt() {
      String prompt = ContextualRelevancyPromptConstants.VERDICT_GENERATION_PROMPT;

      // Check for clear directive language
      assertTrue(prompt.toLowerCase().contains("determine"), "Should contain determination directive");
      assertTrue(prompt.toLowerCase().contains("generate"), "Should contain generation directive");
      assertTrue(prompt.toLowerCase().contains("please"), "Should be polite");
    }

    @Test
    @DisplayName("Should have clear instructions in reason prompt")
    void shouldHaveClearInstructionsInReasonPrompt() {
      String prompt = ContextualRelevancyPromptConstants.REASON_GENERATION_PROMPT;

      // Check for clear directive language
      assertTrue(prompt.toLowerCase().contains("provide"), "Should contain provision directive");
      assertTrue(prompt.toLowerCase().contains("explain"), "Should contain explanation directive");
      assertTrue(prompt.toLowerCase().contains("concise"), "Should emphasize conciseness");
    }

    @Test
    @DisplayName("Should specify output constraints in verdict prompt")
    void shouldSpecifyOutputConstraintsInVerdictPrompt() {
      String prompt = ContextualRelevancyPromptConstants.VERDICT_GENERATION_PROMPT;

      assertTrue(prompt.contains("only 'yes' or 'no'"), "Should specify verdict values");
      assertTrue(prompt.contains("STRICTLY EQUAL"), "Should emphasize count matching");
      assertTrue(prompt.contains("number of 'verdicts'"), "Should mention verdict count");
    }

    @Test
    @DisplayName("Should specify output constraints in reason prompt")
    void shouldSpecifyOutputConstraintsInReasonPrompt() {
      String prompt = ContextualRelevancyPromptConstants.REASON_GENERATION_PROMPT;

      assertTrue(prompt.contains("CONCISE summary"), "Should emphasize conciseness");
      assertTrue(prompt.contains("only return in JSON format"), "Should specify JSON format");
      assertTrue(prompt.contains("'reason' key"), "Should specify reason key");
    }

    @Test
    @DisplayName("Should provide context for evaluation in verdict prompt")
    void shouldProvideContextForEvaluationInVerdictPrompt() {
      String prompt = ContextualRelevancyPromptConstants.VERDICT_GENERATION_PROMPT;

      assertTrue(prompt.toLowerCase().contains("answering the user's question"), "Should mention user's question");
      assertTrue(prompt.toLowerCase().contains("relevant"), "Should mention relevance concept");
      assertTrue(prompt.toLowerCase().contains("context"), "Should mention context concept");
    }

    @Test
    @DisplayName("Should provide context for evaluation in reason prompt")
    void shouldProvideContextForEvaluationInReasonPrompt() {
      String prompt = ContextualRelevancyPromptConstants.REASON_GENERATION_PROMPT;

      assertTrue(prompt.toLowerCase().contains("contextual relevancy"), "Should mention metric name");
      assertTrue(prompt.toLowerCase().contains("proportion"), "Should explain the metric");
      assertTrue(prompt.toLowerCase().contains("higher score"), "Should explain score meaning");
    }

    @Test
    @DisplayName("Should have consistent terminology across prompts")
    void shouldHaveConsistentTerminologyAcrossPrompts() {
      String verdictPrompt = ContextualRelevancyPromptConstants.VERDICT_GENERATION_PROMPT;
      String reasonPrompt = ContextualRelevancyPromptConstants.REASON_GENERATION_PROMPT;

      // Check that key terms are used consistently
      assertTrue(verdictPrompt.contains("retrieval context") && reasonPrompt.contains("retrieval contexts"), 
                 "Should consistently refer to retrieval contexts");
      assertTrue(verdictPrompt.contains("user input") && reasonPrompt.contains("user input"), 
                 "Should consistently refer to user input");
      assertTrue(verdictPrompt.contains("relevant") && reasonPrompt.contains("relevant"), 
                 "Should consistently use relevance terminology");
    }
  }
}
