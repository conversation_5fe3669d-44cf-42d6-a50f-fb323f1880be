package com.openevals4j.metrics.responsecompleteness;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.openevals4j.metrics.exception.EvaluationContextValidationException;
import com.openevals4j.metrics.models.EvaluationContext;
import com.openevals4j.metrics.models.EvaluationResult;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.chat.request.ChatRequest;
import dev.langchain4j.model.chat.response.ChatResponse;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@DisplayName("ResponseCompletenessMetric Comprehensive Tests")
class ResponseCompletenessMetricComprehensiveTest {

  @Mock private ChatLanguageModel mockChatModel;
  @Mock private ChatResponse mockChatResponse;
  @Mock private AiMessage mockAiMessage;

  private ResponseCompletenessMetric responseCompletenessMetric;
  private ObjectMapper objectMapper;

  @BeforeEach
  void setUp() {
    objectMapper = new ObjectMapper();
    responseCompletenessMetric =
        ResponseCompletenessMetric.builder()
            .evaluatorLLM(mockChatModel)
            .objectMapper(objectMapper)
            .build();
  }

  @Nested
  @DisplayName("Constructor and Builder Tests")
  class ConstructorTests {

    @Test
    @DisplayName("Should create metric with valid parameters")
    void shouldCreateMetricWithValidParameters() {
      assertNotNull(responseCompletenessMetric);
      assertEquals("RESPONSE_COMPLETENESS", responseCompletenessMetric.getMetricName().name());
    }

    @Test
    @DisplayName("Should throw exception when evaluatorLLM is null")
    void shouldThrowExceptionWhenEvaluatorLLMIsNull() {
      assertThrows(
          NullPointerException.class,
          () ->
              ResponseCompletenessMetric.builder()
                  .evaluatorLLM(null)
                  .objectMapper(objectMapper)
                  .build());
    }

    @Test
    @DisplayName("Should throw exception when objectMapper is null")
    void shouldThrowExceptionWhenObjectMapperIsNull() {
      assertThrows(
          NullPointerException.class,
          () ->
              ResponseCompletenessMetric.builder()
                  .evaluatorLLM(mockChatModel)
                  .objectMapper(null)
                  .build());
    }
  }

  @Nested
  @DisplayName("Validation Tests")
  class ValidationTests {

    @Test
    @DisplayName("Should throw exception when userInput is missing")
    void shouldThrowExceptionWhenUserInputIsMissing() {
      EvaluationContext context =
          EvaluationContext.builder()
              .expectedResponse("Expected response")
              .actualResponse("Actual response")
              .build();

      assertThrows(
          EvaluationContextValidationException.class,
          () -> responseCompletenessMetric.evaluate(context));
    }

    @Test
    @DisplayName("Should throw exception when expectedResponse is missing")
    void shouldThrowExceptionWhenExpectedResponseIsMissing() {
      EvaluationContext context =
          EvaluationContext.builder()
              .userInput("User input")
              .actualResponse("Actual response")
              .build();

      assertThrows(
          EvaluationContextValidationException.class,
          () -> responseCompletenessMetric.evaluate(context));
    }

    @Test
    @DisplayName("Should throw exception when actualResponse is missing")
    void shouldThrowExceptionWhenActualResponseIsMissing() {
      EvaluationContext context =
          EvaluationContext.builder()
              .userInput("User input")
              .expectedResponse("Expected response")
              .build();

      assertThrows(
          EvaluationContextValidationException.class,
          () -> responseCompletenessMetric.evaluate(context));
    }

    @Test
    @DisplayName("Should throw exception when userInput is empty")
    void shouldThrowExceptionWhenUserInputIsEmpty() {
      EvaluationContext context =
          EvaluationContext.builder()
              .userInput("")
              .expectedResponse("Expected response")
              .actualResponse("Actual response")
              .build();

      assertThrows(
          EvaluationContextValidationException.class,
          () -> responseCompletenessMetric.evaluate(context));
    }

    @Test
    @DisplayName("Should throw exception when expectedResponse is empty")
    void shouldThrowExceptionWhenExpectedResponseIsEmpty() {
      EvaluationContext context =
          EvaluationContext.builder()
              .userInput("User input")
              .expectedResponse("")
              .actualResponse("Actual response")
              .build();

      assertThrows(
          EvaluationContextValidationException.class,
          () -> responseCompletenessMetric.evaluate(context));
    }

    @Test
    @DisplayName("Should throw exception when actualResponse is empty")
    void shouldThrowExceptionWhenActualResponseIsEmpty() {
      EvaluationContext context =
          EvaluationContext.builder()
              .userInput("User input")
              .expectedResponse("Expected response")
              .actualResponse("")
              .build();

      assertThrows(
          EvaluationContextValidationException.class,
          () -> responseCompletenessMetric.evaluate(context));
    }

    @Test
    @DisplayName("Should validate required fields correctly")
    void shouldValidateRequiredFieldsCorrectly() {
      List<String> requiredFields = responseCompletenessMetric.getRequiredFieldsForValidation();
      assertEquals(3, requiredFields.size());
      assertTrue(requiredFields.contains("userInput"));
      assertTrue(requiredFields.contains("expectedResponse"));
      assertTrue(requiredFields.contains("actualResponse"));
    }
  }

  @Nested
  @DisplayName("Evaluation Tests")
  class EvaluationTests {

    @Test
    @DisplayName("Should evaluate complete response successfully")
    void shouldEvaluateCompleteResponseSuccessfully() throws Exception {
      // Arrange
      String mockResponseJson = """
          {
            "score": 5,
            "reasoning": "The response comprehensively covers all aspects of the question with excellent detail and accuracy."
          }
          """;

      when(mockAiMessage.text()).thenReturn(mockResponseJson);
      when(mockChatResponse.aiMessage()).thenReturn(mockAiMessage);
      when(mockChatModel.chat(any(ChatRequest.class))).thenReturn(mockChatResponse);

      EvaluationContext context =
          EvaluationContext.builder()
              .userInput("Explain machine learning and provide examples")
              .expectedResponse("Machine learning is AI that learns from data. Examples: spam detection, recommendations.")
              .actualResponse("Machine learning is a subset of AI that enables computers to learn from data without explicit programming. Examples include email spam detection and recommendation systems.")
              .build();

      // Act
      EvaluationResult result = responseCompletenessMetric.evaluate(context);

      // Assert
      assertNotNull(result);
      assertEquals(5.0, result.getScore());
      assertEquals("The response comprehensively covers all aspects of the question with excellent detail and accuracy.", result.getReasoning());
      verify(mockChatModel, times(1)).chat(any(ChatRequest.class));
    }

    @Test
    @DisplayName("Should evaluate incomplete response successfully")
    void shouldEvaluateIncompleteResponseSuccessfully() throws Exception {
      // Arrange
      String mockResponseJson = """
          {
            "score": 2,
            "reasoning": "The response only covers the definition but completely misses the required examples."
          }
          """;

      when(mockAiMessage.text()).thenReturn(mockResponseJson);
      when(mockChatResponse.aiMessage()).thenReturn(mockAiMessage);
      when(mockChatModel.chat(any(ChatRequest.class))).thenReturn(mockChatResponse);

      EvaluationContext context =
          EvaluationContext.builder()
              .userInput("Explain machine learning and provide examples")
              .expectedResponse("Machine learning is AI that learns from data. Examples: spam detection, recommendations.")
              .actualResponse("Machine learning is a subset of artificial intelligence.")
              .build();

      // Act
      EvaluationResult result = responseCompletenessMetric.evaluate(context);

      // Assert
      assertNotNull(result);
      assertEquals(2.0, result.getScore());
      assertEquals("The response only covers the definition but completely misses the required examples.", result.getReasoning());
    }

    @Test
    @DisplayName("Should handle JSON parsing errors gracefully")
    void shouldHandleJsonParsingErrorsGracefully() throws Exception {
      // Arrange
      String invalidJson = "{ invalid json }";

      when(mockAiMessage.text()).thenReturn(invalidJson);
      when(mockChatResponse.aiMessage()).thenReturn(mockAiMessage);
      when(mockChatModel.chat(any(ChatRequest.class))).thenReturn(mockChatResponse);

      EvaluationContext context =
          EvaluationContext.builder()
              .userInput("Test question")
              .expectedResponse("Expected answer")
              .actualResponse("Actual answer")
              .build();

      // Act
      EvaluationResult result = responseCompletenessMetric.evaluate(context);

      // Assert
      assertNotNull(result);
      assertEquals(0.0, result.getScore()); // Default score
      assertEquals("Unable to evaluate due to an error", result.getReasoning()); // Default reasoning
    }

    @Test
    @DisplayName("Should handle LLM exceptions gracefully")
    void shouldHandleLLMExceptionsGracefully() throws Exception {
      // Arrange
      when(mockChatModel.chat(any(ChatRequest.class))).thenThrow(new RuntimeException("LLM error"));

      EvaluationContext context =
          EvaluationContext.builder()
              .userInput("Test question")
              .expectedResponse("Expected answer")
              .actualResponse("Actual answer")
              .build();

      // Act
      EvaluationResult result = responseCompletenessMetric.evaluate(context);

      // Assert
      assertNotNull(result);
      assertEquals(0.0, result.getScore()); // Default score
      assertEquals("Unable to evaluate due to an error", result.getReasoning()); // Default reasoning
    }
  }

  @Nested
  @DisplayName("Batch Evaluation Tests")
  class BatchEvaluationTests {

    @Test
    @DisplayName("Should evaluate batch of contexts successfully")
    void shouldEvaluateBatchOfContextsSuccessfully() throws Exception {
      // Arrange
      String mockResponse1Json = """
          {
            "score": 4,
            "reasoning": "Good coverage with minor gaps."
          }
          """;

      String mockResponse2Json = """
          {
            "score": 2,
            "reasoning": "Incomplete coverage of key topics."
          }
          """;

      when(mockAiMessage.text()).thenReturn(mockResponse1Json, mockResponse2Json);
      when(mockChatResponse.aiMessage()).thenReturn(mockAiMessage);
      when(mockChatModel.chat(any(ChatRequest.class))).thenReturn(mockChatResponse);

      List<EvaluationContext> contexts = Arrays.asList(
          EvaluationContext.builder()
              .userInput("Question 1")
              .expectedResponse("Expected 1")
              .actualResponse("Actual 1")
              .build(),
          EvaluationContext.builder()
              .userInput("Question 2")
              .expectedResponse("Expected 2")
              .actualResponse("Actual 2")
              .build()
      );

      // Act
      List<EvaluationResult> results = responseCompletenessMetric.evaluateBatch(contexts);

      // Assert
      assertNotNull(results);
      assertEquals(2, results.size());
      assertEquals(4.0, results.get(0).getScore());
      assertEquals(2.0, results.get(1).getScore());
      verify(mockChatModel, times(2)).chat(any(ChatRequest.class));
    }

    @Test
    @DisplayName("Should handle empty batch")
    void shouldHandleEmptyBatch() {
      // Act
      List<EvaluationResult> results = responseCompletenessMetric.evaluateBatch(Arrays.asList());

      // Assert
      assertNotNull(results);
      assertTrue(results.isEmpty());
      verify(mockChatModel, never()).chat(any(ChatRequest.class));
    }

    @Test
    @DisplayName("Should handle batch with mixed valid and invalid contexts")
    void shouldHandleBatchWithMixedValidAndInvalidContexts() throws Exception {
      // Arrange
      String mockResponseJson = """
          {
            "score": 3,
            "reasoning": "Adequate coverage."
          }
          """;

      when(mockAiMessage.text()).thenReturn(mockResponseJson);
      when(mockChatResponse.aiMessage()).thenReturn(mockAiMessage);
      when(mockChatModel.chat(any(ChatRequest.class))).thenReturn(mockChatResponse);

      List<EvaluationContext> contexts = Arrays.asList(
          EvaluationContext.builder()
              .userInput("Valid question")
              .expectedResponse("Valid expected")
              .actualResponse("Valid actual")
              .build(),
          EvaluationContext.builder()
              .userInput("") // Invalid - empty userInput
              .expectedResponse("Expected")
              .actualResponse("Actual")
              .build()
      );

      // Act & Assert
      assertThrows(
          EvaluationContextValidationException.class,
          () -> responseCompletenessMetric.evaluateBatch(contexts));
    }
  }

  @Nested
  @DisplayName("Edge Case Tests")
  class EdgeCaseTests {

    @Test
    @DisplayName("Should handle very long responses")
    void shouldHandleVeryLongResponses() throws Exception {
      // Arrange
      String longText = "A".repeat(10000); // 10k character string
      String mockResponseJson = """
          {
            "score": 3,
            "reasoning": "Response is very long but covers the key points adequately."
          }
          """;

      when(mockAiMessage.text()).thenReturn(mockResponseJson);
      when(mockChatResponse.aiMessage()).thenReturn(mockAiMessage);
      when(mockChatModel.chat(any(ChatRequest.class))).thenReturn(mockChatResponse);

      EvaluationContext context =
          EvaluationContext.builder()
              .userInput("Short question")
              .expectedResponse("Short expected response")
              .actualResponse(longText)
              .build();

      // Act
      EvaluationResult result = responseCompletenessMetric.evaluate(context);

      // Assert
      assertNotNull(result);
      assertEquals(3.0, result.getScore());
      verify(mockChatModel, times(1)).chat(any(ChatRequest.class));
    }

    @Test
    @DisplayName("Should handle special characters in responses")
    void shouldHandleSpecialCharactersInResponses() throws Exception {
      // Arrange
      String specialCharsText = "Response with special chars: @#$%^&*(){}[]|\\:;\"'<>,.?/~`";
      String mockResponseJson = """
          {
            "score": 4,
            "reasoning": "Response handles special characters well and covers the topic."
          }
          """;

      when(mockAiMessage.text()).thenReturn(mockResponseJson);
      when(mockChatResponse.aiMessage()).thenReturn(mockAiMessage);
      when(mockChatModel.chat(any(ChatRequest.class))).thenReturn(mockChatResponse);

      EvaluationContext context =
          EvaluationContext.builder()
              .userInput("Question about special characters")
              .expectedResponse("Expected response with special chars")
              .actualResponse(specialCharsText)
              .build();

      // Act
      EvaluationResult result = responseCompletenessMetric.evaluate(context);

      // Assert
      assertNotNull(result);
      assertEquals(4.0, result.getScore());
    }

    @Test
    @DisplayName("Should handle Unicode characters")
    void shouldHandleUnicodeCharacters() throws Exception {
      // Arrange
      String unicodeText = "Response with Unicode: 你好 🌟 café naïve résumé";
      String mockResponseJson = """
          {
            "score": 5,
            "reasoning": "Response correctly handles Unicode characters and provides complete coverage."
          }
          """;

      when(mockAiMessage.text()).thenReturn(mockResponseJson);
      when(mockChatResponse.aiMessage()).thenReturn(mockAiMessage);
      when(mockChatModel.chat(any(ChatRequest.class))).thenReturn(mockChatResponse);

      EvaluationContext context =
          EvaluationContext.builder()
              .userInput("Question about internationalization")
              .expectedResponse("Expected response with Unicode")
              .actualResponse(unicodeText)
              .build();

      // Act
      EvaluationResult result = responseCompletenessMetric.evaluate(context);

      // Assert
      assertNotNull(result);
      assertEquals(5.0, result.getScore());
    }

    @Test
    @DisplayName("Should handle responses with only whitespace")
    void shouldHandleResponsesWithOnlyWhitespace() {
      // Arrange
      EvaluationContext context =
          EvaluationContext.builder()
              .userInput("Valid question")
              .expectedResponse("Valid expected response")
              .actualResponse("   \t\n   ") // Only whitespace
              .build();

      // Act & Assert
      assertThrows(
          EvaluationContextValidationException.class,
          () -> responseCompletenessMetric.evaluate(context));
    }
  }
}
