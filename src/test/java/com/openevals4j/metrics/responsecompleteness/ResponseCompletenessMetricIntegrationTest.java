package com.openevals4j.metrics.responsecompleteness;

import static org.junit.jupiter.api.Assertions.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.openevals4j.metrics.models.EvaluationContext;
import com.openevals4j.metrics.models.EvaluationResult;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.googleai.GoogleAiGeminiChatModel;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

@DisplayName("ResponseCompletenessMetric Integration Tests")
class ResponseCompletenessMetricIntegrationTest {

  private ResponseCompletenessMetric responseCompletenessMetric;

  @BeforeEach
  void setUp() {
    ChatLanguageModel chatModel =
        GoogleAiGeminiChatModel.builder()
            .apiKey("REPLACE_YOUR_API_KEY_HERE")
            .modelName("gemini-1.5-flash")
            .logRequestsAndResponses(true)
            .build();
    responseCompletenessMetric =
        ResponseCompletenessMetric.builder()
            .evaluatorLLM(chatModel)
            .objectMapper(new ObjectMapper())
            .build();
  }

  @Nested
  @DisplayName("Complete Response Scenarios")
  class CompleteResponseScenarios {

    @Test
    @Disabled("OpenAI and Gemini keys are not free")
    @DisplayName("Should score high for comprehensive machine learning explanation")
    void shouldScoreHighForComprehensiveMachineLearningExplanation() {
      EvaluationResult result =
          responseCompletenessMetric.evaluate(
              EvaluationContext.builder()
                  .userInput("Explain what machine learning is and provide two examples of its applications.")
                  .expectedResponse("Machine learning is a subset of artificial intelligence that enables computers to learn and make decisions from data without being explicitly programmed. Two examples of its applications are: 1) Email spam detection - algorithms learn to identify spam emails based on patterns in email content and metadata, and 2) Recommendation systems - platforms like Netflix and Amazon use ML to suggest content or products based on user behavior and preferences.")
                  .actualResponse("Machine learning is a subset of AI that allows computers to learn from data without explicit programming. Two examples are: 1) Email spam detection where algorithms identify spam based on email patterns, and 2) Recommendation systems like those used by Netflix to suggest movies based on viewing history.")
                  .build());

      assertNotNull(result);
      assertTrue(result.getScore() >= 4.0, "Complete response should score 4 or higher");
      assertNotNull(result.getReasoning());
      assertFalse(result.getReasoning().isEmpty());
    }

    @Test
    @Disabled("OpenAI and Gemini keys are not free")
    @DisplayName("Should score high for detailed renewable energy explanation")
    void shouldScoreHighForDetailedRenewableEnergyExplanation() {
      EvaluationResult result =
          responseCompletenessMetric.evaluate(
              EvaluationContext.builder()
                  .userInput("What are the benefits of renewable energy?")
                  .expectedResponse("Renewable energy offers several benefits: 1) Environmental benefits - reduces greenhouse gas emissions and pollution, 2) Economic benefits - creates jobs and reduces energy costs over time, 3) Energy security - reduces dependence on fossil fuel imports.")
                  .actualResponse("Renewable energy provides multiple advantages: Environmental benefits include reducing greenhouse gas emissions and air pollution. Economic benefits encompass job creation in green industries and long-term cost savings. Energy security benefits involve reducing dependence on fossil fuel imports and increasing energy independence.")
                  .build());

      assertNotNull(result);
      assertTrue(result.getScore() >= 4.0, "Detailed response should score 4 or higher");
      assertNotNull(result.getReasoning());
    }
  }

  @Nested
  @DisplayName("Incomplete Response Scenarios")
  class IncompleteResponseScenarios {

    @Test
    @Disabled("OpenAI and Gemini keys are not free")
    @DisplayName("Should score low for partial machine learning explanation")
    void shouldScoreLowForPartialMachineLearningExplanation() {
      EvaluationResult result =
          responseCompletenessMetric.evaluate(
              EvaluationContext.builder()
                  .userInput("Explain what machine learning is and provide two examples of its applications.")
                  .expectedResponse("Machine learning is a subset of artificial intelligence that enables computers to learn and make decisions from data without being explicitly programmed. Two examples of its applications are: 1) Email spam detection - algorithms learn to identify spam emails based on patterns in email content and metadata, and 2) Recommendation systems - platforms like Netflix and Amazon use ML to suggest content or products based on user behavior and preferences.")
                  .actualResponse("Machine learning is a subset of AI that allows computers to learn from data.")
                  .build());

      assertNotNull(result);
      assertTrue(result.getScore() <= 2.0, "Incomplete response should score 2 or lower");
      assertNotNull(result.getReasoning());
      assertTrue(result.getReasoning().toLowerCase().contains("example") || 
                 result.getReasoning().toLowerCase().contains("missing"), 
                 "Reasoning should mention missing examples");
    }

    @Test
    @Disabled("OpenAI and Gemini keys are not free")
    @DisplayName("Should score low for minimal renewable energy explanation")
    void shouldScoreLowForMinimalRenewableEnergyExplanation() {
      EvaluationResult result =
          responseCompletenessMetric.evaluate(
              EvaluationContext.builder()
                  .userInput("What are the benefits of renewable energy?")
                  .expectedResponse("Renewable energy offers several benefits: 1) Environmental benefits - reduces greenhouse gas emissions and pollution, 2) Economic benefits - creates jobs and reduces energy costs over time, 3) Energy security - reduces dependence on fossil fuel imports.")
                  .actualResponse("Renewable energy is good for the environment.")
                  .build());

      assertNotNull(result);
      assertTrue(result.getScore() <= 3.0, "Minimal response should score 3 or lower");
      assertNotNull(result.getReasoning());
    }
  }

  @Nested
  @DisplayName("Moderate Completeness Scenarios")
  class ModerateCompletenessScenarios {

    @Test
    @Disabled("OpenAI and Gemini keys are not free")
    @DisplayName("Should score moderately for partially complete response")
    void shouldScoreModeratelyForPartiallyCompleteResponse() {
      EvaluationResult result =
          responseCompletenessMetric.evaluate(
              EvaluationContext.builder()
                  .userInput("Explain the water cycle and its main stages.")
                  .expectedResponse("The water cycle is the continuous movement of water through Earth's atmosphere, land, and oceans. Main stages include: 1) Evaporation - water changes from liquid to vapor, 2) Condensation - water vapor forms clouds, 3) Precipitation - water falls as rain or snow, 4) Collection - water gathers in bodies of water and groundwater.")
                  .actualResponse("The water cycle is the movement of water through the environment. It includes evaporation where water becomes vapor, and precipitation where water falls as rain. Water then collects in rivers and oceans.")
                  .build());

      assertNotNull(result);
      assertTrue(result.getScore() >= 2.0 && result.getScore() <= 4.0, 
                 "Partially complete response should score between 2-4");
      assertNotNull(result.getReasoning());
    }
  }

  @Nested
  @DisplayName("Batch Evaluation Scenarios")
  class BatchEvaluationScenarios {

    @Test
    @Disabled("OpenAI and Gemini keys are not free")
    @DisplayName("Should evaluate batch with varying completeness levels")
    void shouldEvaluateBatchWithVaryingCompletenessLevels() {
      List<EvaluationContext> contexts = Arrays.asList(
          EvaluationContext.builder()
              .userInput("What is photosynthesis?")
              .expectedResponse("Photosynthesis is the process by which plants convert sunlight, carbon dioxide, and water into glucose and oxygen using chlorophyll.")
              .actualResponse("Photosynthesis is how plants make food using sunlight, CO2, and water, producing glucose and oxygen.")
              .build(),
          EvaluationContext.builder()
              .userInput("What is photosynthesis?")
              .expectedResponse("Photosynthesis is the process by which plants convert sunlight, carbon dioxide, and water into glucose and oxygen using chlorophyll.")
              .actualResponse("Photosynthesis is when plants use sunlight.")
              .build(),
          EvaluationContext.builder()
              .userInput("What is photosynthesis?")
              .expectedResponse("Photosynthesis is the process by which plants convert sunlight, carbon dioxide, and water into glucose and oxygen using chlorophyll.")
              .actualResponse("Photosynthesis is the biological process where plants, algae, and some bacteria convert light energy into chemical energy. Plants use sunlight, carbon dioxide from air, and water from soil to produce glucose (sugar) and release oxygen as a byproduct. This process occurs in chloroplasts using chlorophyll.")
              .build()
      );

      List<EvaluationResult> results = responseCompletenessMetric.evaluateBatch(contexts);

      assertNotNull(results);
      assertEquals(3, results.size());

      // First response should be good (covers main points)
      assertTrue(results.get(0).getScore() >= 3.0, "Good response should score 3+");
      
      // Second response should be poor (very incomplete)
      assertTrue(results.get(1).getScore() <= 2.0, "Poor response should score 2 or less");
      
      // Third response should be excellent (comprehensive)
      assertTrue(results.get(2).getScore() >= 4.0, "Excellent response should score 4+");

      // Scores should be in ascending order
      assertTrue(results.get(1).getScore() < results.get(0).getScore(), 
                 "Poor response should score lower than good response");
      assertTrue(results.get(0).getScore() < results.get(2).getScore(), 
                 "Good response should score lower than excellent response");

      // All should have reasoning
      results.forEach(result -> {
        assertNotNull(result.getReasoning());
        assertFalse(result.getReasoning().isEmpty());
      });
    }
  }

  @Nested
  @DisplayName("Edge Case Integration Tests")
  class EdgeCaseIntegrationTests {

    @Test
    @Disabled("OpenAI and Gemini keys are not free")
    @DisplayName("Should handle response that exceeds expected scope")
    void shouldHandleResponseThatExceedsExpectedScope() {
      EvaluationResult result =
          responseCompletenessMetric.evaluate(
              EvaluationContext.builder()
                  .userInput("What is gravity?")
                  .expectedResponse("Gravity is a force that attracts objects toward each other, keeping us on Earth.")
                  .actualResponse("Gravity is a fundamental force of nature that attracts objects with mass toward each other. It keeps us on Earth and governs planetary motion. Einstein's theory of general relativity describes gravity as the curvature of spacetime caused by mass and energy. The strength of gravity depends on mass and distance, following Newton's law of universal gravitation: F = G(m1*m2)/r².")
                  .build());

      assertNotNull(result);
      // Should score well despite exceeding expected scope, as it covers all required elements
      assertTrue(result.getScore() >= 4.0, "Response exceeding scope should still score well if complete");
      assertNotNull(result.getReasoning());
    }

    @Test
    @Disabled("OpenAI and Gemini keys are not free")
    @DisplayName("Should handle different but valid approach to answering")
    void shouldHandleDifferentButValidApproachToAnswering() {
      EvaluationResult result =
          responseCompletenessMetric.evaluate(
              EvaluationContext.builder()
                  .userInput("How do you make a peanut butter sandwich?")
                  .expectedResponse("To make a peanut butter sandwich: 1) Get two slices of bread, 2) Spread peanut butter on one slice, 3) Optionally add jelly to the other slice, 4) Put the slices together.")
                  .actualResponse("Start by gathering bread, peanut butter, and optionally jelly. Take one slice and apply peanut butter evenly. If using jelly, spread it on the second slice. Combine the slices with the spreads facing inward to complete your sandwich.")
                  .build());

      assertNotNull(result);
      // Should score well as it covers all essential steps despite different organization
      assertTrue(result.getScore() >= 3.0, "Different but valid approach should score reasonably well");
      assertNotNull(result.getReasoning());
    }
  }
}
