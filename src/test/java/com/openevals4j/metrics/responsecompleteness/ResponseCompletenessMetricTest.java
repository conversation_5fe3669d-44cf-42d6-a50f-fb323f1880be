package com.openevals4j.metrics.responsecompleteness;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.openevals4j.metrics.models.EvaluationContext;
import com.openevals4j.metrics.models.EvaluationResult;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.googleai.GoogleAiGeminiChatModel;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

class ResponseCompletenessMetricTest {

  private ResponseCompletenessMetric responseCompletenessMetric;

  @BeforeEach
  void setUp() {
    ChatLanguageModel chatModel =
        GoogleAiGeminiChatModel.builder()
            .apiKey("REPLACE_YOUR_API_KEY_HERE")
            .modelName("gemini-1.5-flash")
            .logRequestsAndResponses(true)
            .build();
    responseCompletenessMetric =
        ResponseCompletenessMetric.builder()
            .evaluatorLLM(chatModel)
            .objectMapper(new ObjectMapper())
            .build();
  }

  @Test
  @Disabled("OpenAI and Gemini keys are not free")
  void evaluate_CompleteResponse() {
    EvaluationResult evaluationResult =
        responseCompletenessMetric.evaluate(
            EvaluationContext.builder()
                .userInput("Explain what machine learning is and provide two examples of its applications.")
                .expectedResponse("Machine learning is a subset of artificial intelligence that enables computers to learn and make decisions from data without being explicitly programmed. Two examples of its applications are: 1) Email spam detection - algorithms learn to identify spam emails based on patterns in email content and metadata, and 2) Recommendation systems - platforms like Netflix and Amazon use ML to suggest content or products based on user behavior and preferences.")
                .actualResponse("Machine learning is a subset of AI that allows computers to learn from data without explicit programming. Two examples are: 1) Email spam detection where algorithms identify spam based on email patterns, and 2) Recommendation systems like those used by Netflix to suggest movies based on viewing history.")
                .build());

    // Should score high (4-5) as it covers all main points
    Assertions.assertTrue(evaluationResult.getScore() >= 4.0);
  }

  @Test
  @Disabled("OpenAI and Gemini keys are not free")
  void evaluate_IncompleteResponse() {
    EvaluationResult evaluationResult =
        responseCompletenessMetric.evaluate(
            EvaluationContext.builder()
                .userInput("Explain what machine learning is and provide two examples of its applications.")
                .expectedResponse("Machine learning is a subset of artificial intelligence that enables computers to learn and make decisions from data without being explicitly programmed. Two examples of its applications are: 1) Email spam detection - algorithms learn to identify spam emails based on patterns in email content and metadata, and 2) Recommendation systems - platforms like Netflix and Amazon use ML to suggest content or products based on user behavior and preferences.")
                .actualResponse("Machine learning is a subset of AI that allows computers to learn from data.")
                .build());

    // Should score low (1-2) as it only covers the definition but misses the examples
    Assertions.assertTrue(evaluationResult.getScore() <= 2.0);
  }

  @Test
  @Disabled("OpenAI and Gemini keys are not free")
  void evaluateBatch() {
    List<EvaluationContext> input =
        List.of(
            EvaluationContext.builder()
                .userInput("What are the benefits of renewable energy?")
                .expectedResponse("Renewable energy offers several benefits: 1) Environmental benefits - reduces greenhouse gas emissions and pollution, 2) Economic benefits - creates jobs and reduces energy costs over time, 3) Energy security - reduces dependence on fossil fuel imports.")
                .actualResponse("Renewable energy reduces greenhouse gas emissions, creates jobs, and provides energy security by reducing fossil fuel dependence.")
                .build(),
            EvaluationContext.builder()
                .userInput("What are the benefits of renewable energy?")
                .expectedResponse("Renewable energy offers several benefits: 1) Environmental benefits - reduces greenhouse gas emissions and pollution, 2) Economic benefits - creates jobs and reduces energy costs over time, 3) Energy security - reduces dependence on fossil fuel imports.")
                .actualResponse("Renewable energy is good for the environment.")
                .build());

    List<EvaluationResult> evaluationResults = responseCompletenessMetric.evaluateBatch(input);

    // First response should score higher as it covers more aspects
    Assertions.assertTrue(evaluationResults.get(0).getScore() > evaluationResults.get(1).getScore());
    
    // Second response should score low as it only covers environmental benefits
    Assertions.assertTrue(evaluationResults.get(1).getScore() <= 3.0);
  }
}
