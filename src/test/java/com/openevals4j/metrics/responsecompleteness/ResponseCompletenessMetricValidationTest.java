package com.openevals4j.metrics.responsecompleteness;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.openevals4j.metrics.exception.EvaluationContextValidationException;
import com.openevals4j.metrics.models.EvaluationContext;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.googleai.GoogleAiGeminiChatModel;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class ResponseCompletenessMetricValidationTest {

  private ResponseCompletenessMetric responseCompletenessMetric;

  @BeforeEach
  void setUp() {
    ChatLanguageModel chatModel =
        GoogleAiGeminiChatModel.builder()
            .apiKey("REPLACE_YOUR_API_KEY_HERE")
            .modelName("gemini-1.5-flash")
            .logRequestsAndResponses(true)
            .build();
    responseCompletenessMetric =
        ResponseCompletenessMetric.builder()
            .evaluatorLLM(chatModel)
            .objectMapper(new ObjectMapper())
            .build();
  }

  @Test
  void shouldThrowExceptionWhenUserInputIsNull() {
    EvaluationContext context =
        EvaluationContext.builder()
            .userInput(null)
            .expectedResponse("Expected response")
            .actualResponse("Actual response")
            .build();

    Assertions.assertThrows(
        EvaluationContextValidationException.class,
        () -> responseCompletenessMetric.evaluate(context));
  }

  @Test
  void shouldThrowExceptionWhenUserInputIsEmpty() {
    EvaluationContext context =
        EvaluationContext.builder()
            .userInput("")
            .expectedResponse("Expected response")
            .actualResponse("Actual response")
            .build();

    Assertions.assertThrows(
        EvaluationContextValidationException.class,
        () -> responseCompletenessMetric.evaluate(context));
  }

  @Test
  void shouldThrowExceptionWhenExpectedResponseIsNull() {
    EvaluationContext context =
        EvaluationContext.builder()
            .userInput("User input")
            .expectedResponse(null)
            .actualResponse("Actual response")
            .build();

    Assertions.assertThrows(
        EvaluationContextValidationException.class,
        () -> responseCompletenessMetric.evaluate(context));
  }

  @Test
  void shouldThrowExceptionWhenExpectedResponseIsEmpty() {
    EvaluationContext context =
        EvaluationContext.builder()
            .userInput("User input")
            .expectedResponse("")
            .actualResponse("Actual response")
            .build();

    Assertions.assertThrows(
        EvaluationContextValidationException.class,
        () -> responseCompletenessMetric.evaluate(context));
  }

  @Test
  void shouldThrowExceptionWhenActualResponseIsNull() {
    EvaluationContext context =
        EvaluationContext.builder()
            .userInput("User input")
            .expectedResponse("Expected response")
            .actualResponse(null)
            .build();

    Assertions.assertThrows(
        EvaluationContextValidationException.class,
        () -> responseCompletenessMetric.evaluate(context));
  }

  @Test
  void shouldThrowExceptionWhenActualResponseIsEmpty() {
    EvaluationContext context =
        EvaluationContext.builder()
            .userInput("User input")
            .expectedResponse("Expected response")
            .actualResponse("")
            .build();

    Assertions.assertThrows(
        EvaluationContextValidationException.class,
        () -> responseCompletenessMetric.evaluate(context));
  }

  @Test
  void shouldThrowExceptionWhenEvaluationContextIsNull() {
    Assertions.assertThrows(
        EvaluationContextValidationException.class,
        () -> responseCompletenessMetric.evaluate(null));
  }

  @Test
  void shouldValidateRequiredFields() {
    List<String> requiredFields = responseCompletenessMetric.getRequiredFieldsForValidation();
    
    Assertions.assertNotNull(requiredFields);
    Assertions.assertEquals(3, requiredFields.size());
    Assertions.assertTrue(requiredFields.contains("userInput"));
    Assertions.assertTrue(requiredFields.contains("expectedResponse"));
    Assertions.assertTrue(requiredFields.contains("actualResponse"));
  }

  @Test
  void shouldThrowExceptionForBatchWithInvalidContext() {
    List<EvaluationContext> contexts = List.of(
        EvaluationContext.builder()
            .userInput("Valid question")
            .expectedResponse("Valid expected")
            .actualResponse("Valid actual")
            .build(),
        EvaluationContext.builder()
            .userInput("") // Invalid - empty userInput
            .expectedResponse("Expected")
            .actualResponse("Actual")
            .build()
    );

    Assertions.assertThrows(
        EvaluationContextValidationException.class,
        () -> responseCompletenessMetric.evaluateBatch(contexts));
  }

  @Test
  void shouldHandleEmptyBatch() {
    List<EvaluationContext> emptyList = List.of();
    
    List<com.openevals4j.metrics.models.EvaluationResult> results = 
        responseCompletenessMetric.evaluateBatch(emptyList);
    
    Assertions.assertNotNull(results);
    Assertions.assertTrue(results.isEmpty());
  }

  @Test
  void shouldThrowExceptionWhenUserInputIsWhitespaceOnly() {
    EvaluationContext context =
        EvaluationContext.builder()
            .userInput("   \t\n   ")
            .expectedResponse("Expected response")
            .actualResponse("Actual response")
            .build();

    Assertions.assertThrows(
        EvaluationContextValidationException.class,
        () -> responseCompletenessMetric.evaluate(context));
  }

  @Test
  void shouldThrowExceptionWhenExpectedResponseIsWhitespaceOnly() {
    EvaluationContext context =
        EvaluationContext.builder()
            .userInput("User input")
            .expectedResponse("   \t\n   ")
            .actualResponse("Actual response")
            .build();

    Assertions.assertThrows(
        EvaluationContextValidationException.class,
        () -> responseCompletenessMetric.evaluate(context));
  }

  @Test
  void shouldThrowExceptionWhenActualResponseIsWhitespaceOnly() {
    EvaluationContext context =
        EvaluationContext.builder()
            .userInput("User input")
            .expectedResponse("Expected response")
            .actualResponse("   \t\n   ")
            .build();

    Assertions.assertThrows(
        EvaluationContextValidationException.class,
        () -> responseCompletenessMetric.evaluate(context));
  }

  @Test
  void shouldThrowExceptionWhenBuilderMissingEvaluatorLLM() {
    Assertions.assertThrows(
        NullPointerException.class,
        () -> ResponseCompletenessMetric.builder()
            .evaluatorLLM(null)
            .objectMapper(new ObjectMapper())
            .build());
  }

  @Test
  void shouldThrowExceptionWhenBuilderMissingObjectMapper() {
    ChatLanguageModel chatModel =
        GoogleAiGeminiChatModel.builder()
            .apiKey("REPLACE_YOUR_API_KEY_HERE")
            .modelName("gemini-1.5-flash")
            .build();

    Assertions.assertThrows(
        NullPointerException.class,
        () -> ResponseCompletenessMetric.builder()
            .evaluatorLLM(chatModel)
            .objectMapper(null)
            .build());
  }

  @Test
  void shouldCreateMetricWithValidParameters() {
    ChatLanguageModel chatModel =
        GoogleAiGeminiChatModel.builder()
            .apiKey("REPLACE_YOUR_API_KEY_HERE")
            .modelName("gemini-1.5-flash")
            .build();

    ResponseCompletenessMetric metric = ResponseCompletenessMetric.builder()
        .evaluatorLLM(chatModel)
        .objectMapper(new ObjectMapper())
        .build();

    Assertions.assertNotNull(metric);
    Assertions.assertEquals("RESPONSE_COMPLETENESS", metric.getMetricName().name());
  }

  @Test
  void shouldValidateContextWithAllRequiredFields() {
    EvaluationContext validContext =
        EvaluationContext.builder()
            .userInput("What is the capital of France?")
            .expectedResponse("The capital of France is Paris.")
            .actualResponse("Paris is the capital of France.")
            .build();

    // This should not throw an exception during validation
    // We can't test the actual evaluation without API keys, but validation should pass
    Assertions.assertDoesNotThrow(() -> {
      List<String> requiredFields = responseCompletenessMetric.getRequiredFieldsForValidation();
      // Validation logic is internal, but we can verify required fields are correct
      Assertions.assertTrue(requiredFields.contains("userInput"));
      Assertions.assertTrue(requiredFields.contains("expectedResponse"));
      Assertions.assertTrue(requiredFields.contains("actualResponse"));
    });
  }
}
