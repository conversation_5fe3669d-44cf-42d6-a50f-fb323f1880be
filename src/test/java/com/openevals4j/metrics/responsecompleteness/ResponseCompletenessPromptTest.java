package com.openevals4j.metrics.responsecompleteness;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

@DisplayName("ResponseCompleteness Prompt Tests")
class ResponseCompletenessPromptTest {

  @Nested
  @DisplayName("Prompt Structure Tests")
  class PromptStructureTests {

    @Test
    @DisplayName("Should contain all required sections")
    void shouldContainAllRequiredSections() {
      String prompt = ResponseCompletenessPromptConstants.RESPONSE_COMPLETENESS_EVALUATION_PROMPT;

      assertNotNull(prompt);
      assertFalse(prompt.isEmpty());

      // Check for main sections
      assertTrue(prompt.contains("## Overview"), "Should contain Overview section");
      assertTrue(prompt.contains("## Input Format"), "Should contain Input Format section");
      assertTrue(prompt.contains("## Evaluation Scale"), "Should contain Evaluation Scale section");
      assertTrue(prompt.contains("## Evaluation Process"), "Should contain Evaluation Process section");
      assertTrue(prompt.contains("## Scoring Guidelines"), "Should contain Scoring Guidelines section");
      assertTrue(prompt.contains("## Output Format"), "Should contain Output Format section");
    }

    @Test
    @DisplayName("Should have correct number of placeholders")
    void shouldHaveCorrectNumberOfPlaceholders() {
      String prompt = ResponseCompletenessPromptConstants.RESPONSE_COMPLETENESS_EVALUATION_PROMPT;

      // Count %s placeholders
      long placeholderCount = prompt.chars().filter(ch -> ch == '%').count() / 2; // Each %s has 2 % chars
      assertEquals(3, placeholderCount, "Should have exactly 3 placeholders for userInput, expectedResponse, and actualResponse");
    }

    @Test
    @DisplayName("Should contain all scoring levels")
    void shouldContainAllScoringLevels() {
      String prompt = ResponseCompletenessPromptConstants.RESPONSE_COMPLETENESS_EVALUATION_PROMPT;

      // Check for all score levels 1-5
      assertTrue(prompt.contains("| 1 |"), "Should contain score level 1");
      assertTrue(prompt.contains("| 2 |"), "Should contain score level 2");
      assertTrue(prompt.contains("| 3 |"), "Should contain score level 3");
      assertTrue(prompt.contains("| 4 |"), "Should contain score level 4");
      assertTrue(prompt.contains("| 5 |"), "Should contain score level 5");
    }

    @Test
    @DisplayName("Should contain JSON output format specification")
    void shouldContainJsonOutputFormatSpecification() {
      String prompt = ResponseCompletenessPromptConstants.RESPONSE_COMPLETENESS_EVALUATION_PROMPT;

      assertTrue(prompt.contains("JSON format"), "Should mention JSON format");
      assertTrue(prompt.contains("\"score\""), "Should specify score field");
      assertTrue(prompt.contains("\"reasoning\""), "Should specify reasoning field");
      assertTrue(prompt.contains("number between 1 and 5"), "Should specify score range");
    }
  }

  @Nested
  @DisplayName("Prompt Content Tests")
  class PromptContentTests {

    @Test
    @DisplayName("Should mention expected response as reference")
    void shouldMentionExpectedResponseAsReference() {
      String prompt = ResponseCompletenessPromptConstants.RESPONSE_COMPLETENESS_EVALUATION_PROMPT;

      assertTrue(prompt.toLowerCase().contains("expected response"), "Should mention expected response");
      assertTrue(prompt.toLowerCase().contains("reference"), "Should mention using as reference");
      assertTrue(prompt.toLowerCase().contains("benchmark"), "Should mention benchmark concept");
    }

    @Test
    @DisplayName("Should include evaluation process steps")
    void shouldIncludeEvaluationProcessSteps() {
      String prompt = ResponseCompletenessPromptConstants.RESPONSE_COMPLETENESS_EVALUATION_PROMPT;

      assertTrue(prompt.contains("1."), "Should contain numbered steps");
      assertTrue(prompt.contains("2."), "Should contain multiple steps");
      assertTrue(prompt.toLowerCase().contains("analyze"), "Should mention analysis");
      assertTrue(prompt.toLowerCase().contains("identify"), "Should mention identification");
      assertTrue(prompt.toLowerCase().contains("coverage"), "Should mention coverage assessment");
    }

    @Test
    @DisplayName("Should include scoring guidelines")
    void shouldIncludeScoringGuidelines() {
      String prompt = ResponseCompletenessPromptConstants.RESPONSE_COMPLETENESS_EVALUATION_PROMPT;

      assertTrue(prompt.toLowerCase().contains("coverage"), "Should mention coverage");
      assertTrue(prompt.toLowerCase().contains("depth"), "Should mention depth");
      assertTrue(prompt.toLowerCase().contains("missing"), "Should mention missing elements");
      assertTrue(prompt.toLowerCase().contains("comprehensive"), "Should mention comprehensiveness");
    }

    @Test
    @DisplayName("Should specify percentage ranges for scoring")
    void shouldSpecifyPercentageRangesForScoring() {
      String prompt = ResponseCompletenessPromptConstants.RESPONSE_COMPLETENESS_EVALUATION_PROMPT;

      assertTrue(prompt.contains("25%"), "Should contain 25% threshold");
      assertTrue(prompt.contains("50%"), "Should contain 50% threshold");
      assertTrue(prompt.contains("75%"), "Should contain 75% threshold");
      assertTrue(prompt.contains("90%"), "Should contain 90% threshold");
    }
  }

  @Nested
  @DisplayName("Prompt Formatting Tests")
  class PromptFormattingTests {

    @Test
    @DisplayName("Should format correctly with sample inputs")
    void shouldFormatCorrectlyWithSampleInputs() {
      String userInput = "What is machine learning?";
      String expectedResponse = "Machine learning is AI that learns from data.";
      String actualResponse = "ML is a subset of AI.";

      String formattedPrompt = String.format(
          ResponseCompletenessPromptConstants.RESPONSE_COMPLETENESS_EVALUATION_PROMPT,
          userInput,
          expectedResponse,
          actualResponse
      );

      assertNotNull(formattedPrompt);
      assertTrue(formattedPrompt.contains(userInput), "Should contain user input");
      assertTrue(formattedPrompt.contains(expectedResponse), "Should contain expected response");
      assertTrue(formattedPrompt.contains(actualResponse), "Should contain actual response");
      assertFalse(formattedPrompt.contains("%s"), "Should not contain unformatted placeholders");
    }

    @Test
    @DisplayName("Should handle special characters in formatting")
    void shouldHandleSpecialCharactersInFormatting() {
      String userInput = "Question with \"quotes\" and 'apostrophes'?";
      String expectedResponse = "Response with special chars: @#$%^&*()";
      String actualResponse = "Answer with newlines\nand\ttabs";

      String formattedPrompt = String.format(
          ResponseCompletenessPromptConstants.RESPONSE_COMPLETENESS_EVALUATION_PROMPT,
          userInput,
          expectedResponse,
          actualResponse
      );

      assertNotNull(formattedPrompt);
      assertTrue(formattedPrompt.contains(userInput), "Should handle quotes in user input");
      assertTrue(formattedPrompt.contains(expectedResponse), "Should handle special chars in expected response");
      assertTrue(formattedPrompt.contains(actualResponse), "Should handle newlines and tabs in actual response");
    }

    @Test
    @DisplayName("Should handle empty strings in formatting")
    void shouldHandleEmptyStringsInFormatting() {
      String userInput = "";
      String expectedResponse = "";
      String actualResponse = "";

      String formattedPrompt = String.format(
          ResponseCompletenessPromptConstants.RESPONSE_COMPLETENESS_EVALUATION_PROMPT,
          userInput,
          expectedResponse,
          actualResponse
      );

      assertNotNull(formattedPrompt);
      assertFalse(formattedPrompt.contains("%s"), "Should not contain unformatted placeholders");
      // The prompt should still be valid even with empty inputs
      assertTrue(formattedPrompt.contains("## Overview"), "Should still contain prompt structure");
    }

    @Test
    @DisplayName("Should handle very long inputs in formatting")
    void shouldHandleVeryLongInputsInFormatting() {
      String userInput = "A".repeat(1000);
      String expectedResponse = "B".repeat(2000);
      String actualResponse = "C".repeat(1500);

      String formattedPrompt = String.format(
          ResponseCompletenessPromptConstants.RESPONSE_COMPLETENESS_EVALUATION_PROMPT,
          userInput,
          expectedResponse,
          actualResponse
      );

      assertNotNull(formattedPrompt);
      assertTrue(formattedPrompt.contains(userInput), "Should handle long user input");
      assertTrue(formattedPrompt.contains(expectedResponse), "Should handle long expected response");
      assertTrue(formattedPrompt.contains(actualResponse), "Should handle long actual response");
      assertFalse(formattedPrompt.contains("%s"), "Should not contain unformatted placeholders");
    }
  }

  @Nested
  @DisplayName("Prompt Quality Tests")
  class PromptQualityTests {

    @Test
    @DisplayName("Should have clear instructions")
    void shouldHaveClearInstructions() {
      String prompt = ResponseCompletenessPromptConstants.RESPONSE_COMPLETENESS_EVALUATION_PROMPT;

      // Check for clear directive language
      assertTrue(prompt.toLowerCase().contains("evaluate"), "Should contain evaluation directive");
      assertTrue(prompt.toLowerCase().contains("compare"), "Should mention comparison");
      assertTrue(prompt.toLowerCase().contains("provide"), "Should ask for output provision");
    }

    @Test
    @DisplayName("Should specify output constraints")
    void shouldSpecifyOutputConstraints() {
      String prompt = ResponseCompletenessPromptConstants.RESPONSE_COMPLETENESS_EVALUATION_PROMPT;

      assertTrue(prompt.contains("between 1 and 5"), "Should specify score range");
      assertTrue(prompt.toLowerCase().contains("detailed analysis"), "Should ask for detailed reasoning");
      assertTrue(prompt.contains("JSON"), "Should specify JSON format");
    }

    @Test
    @DisplayName("Should provide context for evaluation")
    void shouldProvideContextForEvaluation() {
      String prompt = ResponseCompletenessPromptConstants.RESPONSE_COMPLETENESS_EVALUATION_PROMPT;

      assertTrue(prompt.toLowerCase().contains("completeness"), "Should mention completeness concept");
      assertTrue(prompt.toLowerCase().contains("thoroughly"), "Should mention thoroughness");
      assertTrue(prompt.toLowerCase().contains("aspects"), "Should mention different aspects");
      assertTrue(prompt.toLowerCase().contains("requirements"), "Should mention requirements");
    }

    @Test
    @DisplayName("Should have consistent terminology")
    void shouldHaveConsistentTerminology() {
      String prompt = ResponseCompletenessPromptConstants.RESPONSE_COMPLETENESS_EVALUATION_PROMPT;

      // Check that key terms are used consistently
      int userQuestionCount = countOccurrences(prompt.toLowerCase(), "user question");
      int expectedResponseCount = countOccurrences(prompt.toLowerCase(), "expected response");
      int assistantResponseCount = countOccurrences(prompt.toLowerCase(), "assistant");

      assertTrue(userQuestionCount > 0, "Should consistently refer to user question");
      assertTrue(expectedResponseCount > 0, "Should consistently refer to expected response");
      assertTrue(assistantResponseCount > 0, "Should consistently refer to assistant response");
    }

    private int countOccurrences(String text, String substring) {
      int count = 0;
      int index = 0;
      while ((index = text.indexOf(substring, index)) != -1) {
        count++;
        index += substring.length();
      }
      return count;
    }
  }
}
